{"name": "store", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm run --parallel /^dev:/", "dev:frontend": "next dev", "dev:frontend:turbo": "next dev --turbopack", "dev:backend": "convex dev --typecheck=disable", "seed": "convex dev --once && convex run init", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint src --fix", "clean": "git clean -xdf .next .turbo node_modules", "prepare": "pnpm dlx husky install"}, "dependencies": {"@clerk/backend": "^1.30.0", "@clerk/nextjs": "^6.18.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "convex-ents": "^0.13.0", "convex-helpers": "^0.1.79", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.503.0", "next": "15.3.1", "next-international": "^1.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "svix": "^1.64.1", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@eslint/eslintrc": "^3", "@t3-oss/env-nextjs": "^0.13.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.25.1", "eslint-config-next": "15.3.1", "eslint-plugin-format": "^1.0.1", "lint-staged": "^15.5.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}, "lint-staged": {"*": "pnpm lint:fix"}}